import React from 'react';
import { View, Text, StyleSheet, SafeAreaView } from 'react-native';
import { useRouter } from 'expo-router';
import Button from '../../../components/ui/Button';
import { useQuestionnaire } from '../../../contexts/QuestionnaireContext';

export default function QuestionnaireCompleteScreen() {
  const router = useRouter();
  const { reset } = useQuestionnaire();

  const handleFinish = () => {
    // Later, this will redirect to the pre-filled profile form
    reset();
    router.replace('/(app)/home' as any);
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.container}>
        <View style={styles.textContainer}>
          <Text style={styles.title}>All Done!</Text>
          <Text style={styles.subtitle}>You've successfully completed the questionnaire.</Text>
        </View>
        <Button title="See Profile Preview" onPress={handleFinish} />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#fff',
  },
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  textContainer: {
    alignItems: 'center',
    marginBottom: 32,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 16,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    color: '#666',
  },
});