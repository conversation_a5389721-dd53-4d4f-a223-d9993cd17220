import React, { createContext, useContext, useState, ReactNode } from 'react';

// 1. DEFINE THE DATA STRUCTURE
interface QuestionnaireData {
  basics: { name: string; relationship: string };
  dates: { birthday?: Date; anniversary?: Date };
  style: { favoriteColor?: string; preferredStyle?: string; brands: string[] };
  interests: { interests: string[]; dislikes: string[] };
  practical: { sizes?: object; budget?: { min: number; max: number } };
}

// 2. DEFINE THE CONTEXT TYPE
interface QuestionnaireContextType {
  data: Partial<QuestionnaireData>;
  currentStep: number;
  updateData: (newData: Partial<QuestionnaireData>) => void;
  nextStep: () => void;
  prevStep: () => void;
  reset: () => void;
}

// 3. CREATE THE CONTEXT
const QuestionnaireContext = createContext<QuestionnaireContextType | undefined>(undefined);

// 4. CREATE THE PROVIDER COMPONENT
const initialData: Partial<QuestionnaireData> = {
  basics: { name: '', relationship: '' },
  dates: {},
  style: { brands: [] },
  interests: { interests: [], dislikes: [] },
  practical: {},
};

export const QuestionnaireProvider = ({ children }: { children: ReactNode }) => {
  const [data, setData] = useState<Partial<QuestionnaireData>>(initialData);
  const [currentStep, setCurrentStep] = useState(0);

  const updateData = (newData: Partial<QuestionnaireData>) => {
    setData((prevData) => ({ ...prevData, ...newData }));
  };

  const nextStep = () => setCurrentStep((prev) => prev + 1);
  const prevStep = () => setCurrentStep((prev) => (prev > 0 ? prev - 1 : 0));
  const reset = () => {
    setData(initialData);
    setCurrentStep(0);
  };

  return (
    <QuestionnaireContext.Provider value={{ data, currentStep, updateData, nextStep, prevStep, reset }}>
      {children}
    </QuestionnaireContext.Provider>
  );
};

// 5. CREATE THE CUSTOM HOOK
export const useQuestionnaire = () => {
  const context = useContext(QuestionnaireContext);
  if (context === undefined) {
    throw new Error('useQuestionnaire must be used within a QuestionnaireProvider');
  }
  return context;
};