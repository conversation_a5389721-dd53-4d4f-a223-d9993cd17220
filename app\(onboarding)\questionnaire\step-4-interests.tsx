import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView, TextInput } from 'react-native';
import { useRouter } from 'expo-router';
import { Feather } from '@expo/vector-icons';
import { useQuestionnaire } from '../../../contexts/QuestionnaireContext';
import StepLayout from '../../../components/questionnaire/StepLayout';
import * as Haptics from 'expo-haptics';

const TOTAL_STEPS = 5;

// Pre-populated interest suggestions
const INTEREST_SUGGESTIONS = [
  'Reading', 'Cooking', 'Travel', 'Music', 'Movies', 'Sports', 'Gaming', 'Art',
  'Photography', 'Fitness', 'Fashion', 'Technology', 'Gardening', 'Dancing',
  'Writing', 'Hiking', 'Yoga', 'Coffee', 'Wine', 'Crafts', 'Board Games',
  'Pets', 'Cars', 'Cycling', 'Swimming', 'Running', 'Meditation', 'Podcasts'
];

// Common dislikes
const DISLIKE_SUGGESTIONS = [
  'Crowds', 'Loud noises', 'Spicy food', 'Horror movies', 'Early mornings',
  'Cold weather', 'Hot weather', 'Insects', 'Clutter', 'Waiting in lines',
  'Small talk', 'Traffic', 'Seafood', 'Public speaking', 'Cleaning',
  'Shopping', 'Exercising', 'Cooking', 'Technology', 'Social media'
];

export default function Step4InterestsScreen() {
  const router = useRouter();
  const { data, updateData, nextStep } = useQuestionnaire();

  const [selectedInterests, setSelectedInterests] = useState<string[]>(data.interests?.interests || []);
  const [selectedDislikes, setSelectedDislikes] = useState<string[]>(data.interests?.dislikes || []);
  const [customInterest, setCustomInterest] = useState('');
  const [customDislike, setCustomDislike] = useState('');

  // Update context when local state changes
  useEffect(() => {
    updateData({
      interests: {
        interests: selectedInterests,
        dislikes: selectedDislikes,
      },
    });
  }, [selectedInterests, selectedDislikes, updateData]);

  const handleNext = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    nextStep();
    router.push('/(onboarding)/questionnaire/step-5-practical' as any);
  };

  const toggleInterest = (interest: string) => {
    setSelectedInterests(prev => {
      const isSelected = prev.includes(interest);
      const newInterests = isSelected
        ? prev.filter(i => i !== interest)
        : [...prev, interest];

      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      return newInterests;
    });
  };

  const toggleDislike = (dislike: string) => {
    setSelectedDislikes(prev => {
      const isSelected = prev.includes(dislike);
      const newDislikes = isSelected
        ? prev.filter(d => d !== dislike)
        : [...prev, dislike];

      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      return newDislikes;
    });
  };

  const addCustomInterest = () => {
    if (customInterest.trim() && !selectedInterests.includes(customInterest.trim())) {
      setSelectedInterests(prev => [...prev, customInterest.trim()]);
      setCustomInterest('');
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };

  const addCustomDislike = () => {
    if (customDislike.trim() && !selectedDislikes.includes(customDislike.trim())) {
      setSelectedDislikes(prev => [...prev, customDislike.trim()]);
      setCustomDislike('');
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };

  const removeInterest = (interest: string) => {
    setSelectedInterests(prev => prev.filter(i => i !== interest));
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const removeDislike = (dislike: string) => {
    setSelectedDislikes(prev => prev.filter(d => d !== dislike));
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const getPersonName = () => {
    return data.basics?.name || 'they';
  };

  const renderBubble = (
    item: string,
    isSelected: boolean,
    onPress: () => void,
    onRemove?: () => void,
    type: 'interest' | 'dislike' = 'interest'
  ) => (
    <TouchableOpacity
      key={item}
      style={[
        styles.bubble,
        isSelected && (type === 'interest' ? styles.bubbleSelectedInterest : styles.bubbleSelectedDislike),
      ]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <Text style={[
        styles.bubbleText,
        isSelected && (type === 'interest' ? styles.bubbleTextSelectedInterest : styles.bubbleTextSelectedDislike),
      ]}>
        {item}
      </Text>
      {isSelected && onRemove && (
        <TouchableOpacity
          style={styles.removeButton}
          onPress={onRemove}
          hitSlop={{ top: 5, bottom: 5, left: 5, right: 5 }}
        >
          <Feather name="x" size={12} color={type === 'interest' ? '#10b981' : '#ef4444'} />
        </TouchableOpacity>
      )}
    </TouchableOpacity>
  );

  return (
    <StepLayout totalSteps={TOTAL_STEPS} onNext={handleNext}>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        <Text style={styles.title}>What makes {getPersonName()} happy?</Text>
        <Text style={styles.subtitle}>
          Understanding their interests and dislikes helps us suggest perfect gifts
        </Text>

        {/* Interests Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>💚 Interests & Hobbies</Text>
          <Text style={styles.sectionSubtitle}>Optional • Select all that apply</Text>

          <View style={styles.bubbleContainer}>
            {INTEREST_SUGGESTIONS.map((interest) =>
              renderBubble(
                interest,
                selectedInterests.includes(interest),
                () => toggleInterest(interest),
                () => removeInterest(interest),
                'interest'
              )
            )}
          </View>

          {/* Custom Interest Input */}
          <View style={styles.customInputContainer}>
            <TextInput
              style={styles.customInput}
              value={customInterest}
              onChangeText={setCustomInterest}
              placeholder="Add a custom interest..."
              placeholderTextColor="#999"
              returnKeyType="done"
              onSubmitEditing={addCustomInterest}
            />
            <TouchableOpacity
              style={[styles.addButton, !customInterest.trim() && styles.addButtonDisabled]}
              onPress={addCustomInterest}
              disabled={!customInterest.trim()}
            >
              <Feather name="plus" size={16} color={customInterest.trim() ? '#10b981' : '#999'} />
            </TouchableOpacity>
          </View>

          {/* Selected Interests */}
          {selectedInterests.length > 0 && (
            <View style={styles.selectedContainer}>
              <Text style={styles.selectedTitle}>Selected interests:</Text>
              <View style={styles.bubbleContainer}>
                {selectedInterests.map((interest) =>
                  renderBubble(
                    interest,
                    true,
                    () => removeInterest(interest),
                    () => removeInterest(interest),
                    'interest'
                  )
                )}
              </View>
            </View>
          )}
        </View>

        {/* Dislikes Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>❌ Things to Avoid</Text>
          <Text style={styles.sectionSubtitle}>Optional • Help us avoid gift mistakes</Text>

          <View style={styles.bubbleContainer}>
            {DISLIKE_SUGGESTIONS.map((dislike) =>
              renderBubble(
                dislike,
                selectedDislikes.includes(dislike),
                () => toggleDislike(dislike),
                () => removeDislike(dislike),
                'dislike'
              )
            )}
          </View>

          {/* Custom Dislike Input */}
          <View style={styles.customInputContainer}>
            <TextInput
              style={styles.customInput}
              value={customDislike}
              onChangeText={setCustomDislike}
              placeholder="Add something they dislike..."
              placeholderTextColor="#999"
              returnKeyType="done"
              onSubmitEditing={addCustomDislike}
            />
            <TouchableOpacity
              style={[styles.addButton, styles.addButtonDislike, !customDislike.trim() && styles.addButtonDisabled]}
              onPress={addCustomDislike}
              disabled={!customDislike.trim()}
            >
              <Feather name="plus" size={16} color={customDislike.trim() ? '#ef4444' : '#999'} />
            </TouchableOpacity>
          </View>

          {/* Selected Dislikes */}
          {selectedDislikes.length > 0 && (
            <View style={styles.selectedContainer}>
              <Text style={styles.selectedTitle}>Things to avoid:</Text>
              <View style={styles.bubbleContainer}>
                {selectedDislikes.map((dislike) =>
                  renderBubble(
                    dislike,
                    true,
                    () => removeDislike(dislike),
                    () => removeDislike(dislike),
                    'dislike'
                  )
                )}
              </View>
            </View>
          )}
        </View>
      </ScrollView>
    </StepLayout>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 8,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
    color: '#1a1a1a',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    color: '#666',
    marginBottom: 32,
    lineHeight: 22,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 4,
    color: '#1a1a1a',
  },
  sectionSubtitle: {
    fontSize: 14,
    color: '#A3002B',
    fontWeight: '500',
    marginBottom: 16,
  },
  bubbleContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 16,
  },
  bubble: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#e1e5e9',
    backgroundColor: '#fff',
  },
  bubbleSelectedInterest: {
    backgroundColor: '#f0fdf4',
    borderColor: '#10b981',
  },
  bubbleSelectedDislike: {
    backgroundColor: '#fef2f2',
    borderColor: '#ef4444',
  },
  bubbleText: {
    fontSize: 14,
    color: '#666',
  },
  bubbleTextSelectedInterest: {
    color: '#10b981',
    fontWeight: '500',
  },
  bubbleTextSelectedDislike: {
    color: '#ef4444',
    fontWeight: '500',
  },
  removeButton: {
    marginLeft: 6,
    padding: 2,
  },
  customInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  customInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#e1e5e9',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 14,
    backgroundColor: '#fff',
    marginRight: 8,
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f0fdf4',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#10b981',
  },
  addButtonDisabled: {
    backgroundColor: '#f9fafb',
    borderColor: '#e5e7eb',
  },
  addButtonDislike: {
    backgroundColor: '#fef2f2',
    borderColor: '#ef4444',
  },
  selectedContainer: {
    marginTop: 16,
  },
  selectedTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1a1a1a',
    marginBottom: 8,
  },
});