import React from 'react';
import { Text, StyleSheet } from 'react-native';
import { useRouter } from 'expo-router';
import { useQuestionnaire } from '../../../contexts/QuestionnaireContext';
import StepLayout from '../../../components/questionnaire/StepLayout';

const TOTAL_STEPS = 5;

export default function Step1BasicsScreen() {
  const router = useRouter();
  const { nextStep } = useQuestionnaire();

  const handleNext = () => {
    nextStep();
    router.push('/(onboarding)/questionnaire/step-2-dates' as any);
  };

  return (
    <StepLayout totalSteps={TOTAL_STEPS} onNext={handleNext}>
      <Text style={styles.title}>Step 1: The Basics</Text>
      {/* Form fields will go here */}
    </StepLayout>
  );
}

const styles = StyleSheet.create({
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 16,
  },
});