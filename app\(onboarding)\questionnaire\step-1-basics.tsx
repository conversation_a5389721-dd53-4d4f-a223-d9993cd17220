import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import { useRouter } from 'expo-router';
import { Feather } from '@expo/vector-icons';
import { useQuestionnaire } from '../../../contexts/QuestionnaireContext';
import StepLayout from '../../../components/questionnaire/StepLayout';
import * as Haptics from 'expo-haptics';

const TOTAL_STEPS = 5;

// Relationship options with icons
const RELATIONSHIP_OPTIONS = [
  { value: 'partner', label: 'Partner', icon: '❤️', description: 'Spouse, boyfriend, girlfriend' },
  { value: 'family', label: 'Family', icon: '👨‍👩‍👧‍👦', description: 'Parent, sibling, child' },
  { value: 'friend', label: 'Friend', icon: '👥', description: 'Close friend, best friend' },
  { value: 'colleague', label: 'Colleague', icon: '💼', description: 'Coworker, business partner' },
  { value: 'other', label: 'Other', icon: '🤝', description: 'Someone special to you' },
];

export default function Step1BasicsScreen() {
  const router = useRouter();
  const { data, updateData, nextStep } = useQuestionnaire();

  const [name, setName] = useState(data.basics?.name || '');
  const [selectedRelationship, setSelectedRelationship] = useState(data.basics?.relationship || '');
  const [nameError, setNameError] = useState('');

  // Update context when local state changes
  useEffect(() => {
    updateData({
      basics: {
        name: name.trim(),
        relationship: selectedRelationship,
      },
    });
  }, [name, selectedRelationship, updateData]);

  const validateAndProceed = () => {
    // Reset errors
    setNameError('');

    // Validate name
    if (!name.trim()) {
      setNameError('Please enter a name');
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      return;
    }

    if (name.trim().length < 2) {
      setNameError('Name must be at least 2 characters');
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      return;
    }

    // Validate relationship
    if (!selectedRelationship) {
      Alert.alert('Select Relationship', 'Please select how this person relates to you');
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      return;
    }

    // Success feedback
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    // Proceed to next step
    nextStep();
    router.push('/(onboarding)/questionnaire/step-2-dates' as any);
  };

  const handleRelationshipSelect = (relationship: string) => {
    setSelectedRelationship(relationship);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const isFormValid = name.trim().length >= 2 && selectedRelationship;

  return (
    <StepLayout
      totalSteps={TOTAL_STEPS}
      onNext={validateAndProceed}
      isNextDisabled={!isFormValid}
    >
      <View style={styles.container}>
        <Text style={styles.title}>Tell us about someone special</Text>
        <Text style={styles.subtitle}>
          Let's start with the basics so we can create the perfect profile
        </Text>

        {/* Name Input */}
        <View style={styles.inputSection}>
          <Text style={styles.label}>What's their name?</Text>
          <TextInput
            style={[styles.textInput, nameError ? styles.inputError : null]}
            value={name}
            onChangeText={setName}
            placeholder="Enter their name"
            placeholderTextColor="#999"
            autoCapitalize="words"
            autoCorrect={false}
            returnKeyType="next"
          />
          {nameError ? <Text style={styles.errorText}>{nameError}</Text> : null}
        </View>

        {/* Relationship Selector */}
        <View style={styles.inputSection}>
          <Text style={styles.label}>How do they relate to you?</Text>
          <View style={styles.relationshipGrid}>
            {RELATIONSHIP_OPTIONS.map((option) => (
              <TouchableOpacity
                key={option.value}
                style={[
                  styles.relationshipCard,
                  selectedRelationship === option.value && styles.relationshipCardSelected,
                ]}
                onPress={() => handleRelationshipSelect(option.value)}
                activeOpacity={0.7}
              >
                <Text style={styles.relationshipIcon}>{option.icon}</Text>
                <Text style={[
                  styles.relationshipLabel,
                  selectedRelationship === option.value && styles.relationshipLabelSelected,
                ]}>
                  {option.label}
                </Text>
                <Text style={[
                  styles.relationshipDescription,
                  selectedRelationship === option.value && styles.relationshipDescriptionSelected,
                ]}>
                  {option.description}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>
    </StepLayout>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 8,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
    color: '#1a1a1a',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    color: '#666',
    marginBottom: 32,
    lineHeight: 22,
  },
  inputSection: {
    marginBottom: 32,
  },
  label: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
    color: '#1a1a1a',
  },
  textInput: {
    borderWidth: 2,
    borderColor: '#e1e5e9',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    backgroundColor: '#fff',
    color: '#1a1a1a',
  },
  inputError: {
    borderColor: '#ef4444',
  },
  errorText: {
    color: '#ef4444',
    fontSize: 14,
    marginTop: 8,
    marginLeft: 4,
  },
  relationshipGrid: {
    gap: 12,
  },
  relationshipCard: {
    borderWidth: 2,
    borderColor: '#e1e5e9',
    borderRadius: 16,
    padding: 16,
    backgroundColor: '#fff',
    alignItems: 'center',
  },
  relationshipCardSelected: {
    borderColor: '#A3002B',
    backgroundColor: '#fef7f7',
  },
  relationshipIcon: {
    fontSize: 32,
    marginBottom: 8,
  },
  relationshipLabel: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
    color: '#1a1a1a',
  },
  relationshipLabelSelected: {
    color: '#A3002B',
  },
  relationshipDescription: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  relationshipDescriptionSelected: {
    color: '#A3002B',
  },
});