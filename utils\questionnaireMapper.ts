import { Timestamp } from 'firebase/firestore';

// Types for questionnaire data
interface QuestionnaireData {
  basics: { name: string; relationship: string };
  dates: { birthday?: Date; anniversary?: Date };
  style: { favoriteColor?: string; preferredStyle?: string; brands: string[] };
  interests: { interests: string[]; dislikes: string[] };
  practical: { sizes?: { clothing?: string; shoe?: string }; budget?: { min: number; max: number } };
}

// Types for profile form data (matching the existing CreateProfileData interface)
interface ProfileFormData {
  name: string;
  relationship: string;
  birthday: Date | null;
  anniversary: Date | null;
  interestsInput: string;
  dislikesInput: string;
  preferences: {
    favoriteColor: string;
    preferredStyle: string;
    favoriteBrands: string[];
    budgetMin?: number;
    budgetMax?: number;
  };
  sizes: {
    clothing: string;
    shoe: string;
  };
}

/**
 * Maps questionnaire data to profile form data format
 * @param questionnaireData - Data collected from the questionnaire
 * @returns ProfileFormData - Data formatted for the profile creation form
 */
export const mapQuestionnaireToProfileForm = (
  questionnaireData: Partial<QuestionnaireData>
): Partial<ProfileFormData> => {
  const mapped: Partial<ProfileFormData> = {};

  // Map basic information
  if (questionnaireData.basics) {
    mapped.name = questionnaireData.basics.name || '';
    mapped.relationship = questionnaireData.basics.relationship || '';
  }

  // Map dates
  if (questionnaireData.dates) {
    mapped.birthday = questionnaireData.dates.birthday || null;
    mapped.anniversary = questionnaireData.dates.anniversary || null;
  }

  // Map interests and dislikes (convert arrays to comma-separated strings)
  if (questionnaireData.interests) {
    mapped.interestsInput = questionnaireData.interests.interests?.join(', ') || '';
    mapped.dislikesInput = questionnaireData.interests.dislikes?.join(', ') || '';
  }

  // Map style preferences
  if (questionnaireData.style || questionnaireData.practical?.budget) {
    mapped.preferences = {
      favoriteColor: questionnaireData.style?.favoriteColor || '',
      preferredStyle: questionnaireData.style?.preferredStyle || '',
      favoriteBrands: questionnaireData.style?.brands || [],
      budgetMin: questionnaireData.practical?.budget?.min,
      budgetMax: questionnaireData.practical?.budget?.max,
    };
  }

  // Map sizes
  if (questionnaireData.practical?.sizes) {
    mapped.sizes = {
      clothing: questionnaireData.practical.sizes.clothing || '',
      shoe: questionnaireData.practical.sizes.shoe || '',
    };
  }

  return mapped;
};

/**
 * Maps questionnaire data to the CreateProfileData format for backend submission
 * @param questionnaireData - Data collected from the questionnaire
 * @returns CreateProfileData - Data formatted for the backend API
 */
export const mapQuestionnaireToCreateProfile = (
  questionnaireData: Partial<QuestionnaireData>
) => {
  const mapped: any = {};

  // Map basic information
  if (questionnaireData.basics) {
    mapped.name = questionnaireData.basics.name?.trim() || '';
    mapped.relationship = questionnaireData.basics.relationship?.trim() || '';
  }

  // Map dates (convert to Timestamps)
  if (questionnaireData.dates) {
    mapped.birthday = questionnaireData.dates.birthday 
      ? Timestamp.fromDate(questionnaireData.dates.birthday) 
      : null;
    mapped.anniversary = questionnaireData.dates.anniversary 
      ? Timestamp.fromDate(questionnaireData.dates.anniversary) 
      : null;
    
    // Generate MonthDay fields for recurring date calculations
    if (questionnaireData.dates.birthday) {
      const birthday = questionnaireData.dates.birthday;
      mapped.birthdayMonthDay = `${String(birthday.getMonth() + 1).padStart(2, '0')}-${String(birthday.getDate()).padStart(2, '0')}`;
    }
    
    if (questionnaireData.dates.anniversary) {
      const anniversary = questionnaireData.dates.anniversary;
      mapped.anniversaryMonthDay = `${String(anniversary.getMonth() + 1).padStart(2, '0')}-${String(anniversary.getDate()).padStart(2, '0')}`;
    }
  }

  // Map interests and dislikes
  if (questionnaireData.interests) {
    mapped.interests = questionnaireData.interests.interests || [];
    mapped.dislikes = questionnaireData.interests.dislikes || [];
  }

  // Map preferences
  const preferences: any = {};
  if (questionnaireData.style) {
    if (questionnaireData.style.favoriteColor) {
      preferences.favoriteColor = questionnaireData.style.favoriteColor;
    }
    if (questionnaireData.style.preferredStyle) {
      preferences.preferredStyle = questionnaireData.style.preferredStyle;
    }
    if (questionnaireData.style.brands?.length > 0) {
      preferences.favoriteBrands = questionnaireData.style.brands;
    }
  }
  
  if (questionnaireData.practical?.budget) {
    preferences.budgetMin = questionnaireData.practical.budget.min;
    preferences.budgetMax = questionnaireData.practical.budget.max;
  }
  
  if (Object.keys(preferences).length > 0) {
    mapped.preferences = preferences;
  }

  // Map sizes
  const sizes: any = {};
  if (questionnaireData.practical?.sizes) {
    if (questionnaireData.practical.sizes.clothing) {
      sizes.clothing = questionnaireData.practical.sizes.clothing;
    }
    if (questionnaireData.practical.sizes.shoe) {
      sizes.shoe = questionnaireData.practical.sizes.shoe;
    }
  }
  
  if (Object.keys(sizes).length > 0) {
    mapped.sizes = sizes;
  }

  return mapped;
};

/**
 * Checks if questionnaire data is complete enough to create a profile
 * @param questionnaireData - Data collected from the questionnaire
 * @returns boolean - Whether the data is sufficient for profile creation
 */
export const isQuestionnaireDataComplete = (
  questionnaireData: Partial<QuestionnaireData>
): boolean => {
  // At minimum, we need name and relationship
  return !!(
    questionnaireData.basics?.name?.trim() &&
    questionnaireData.basics?.relationship?.trim()
  );
};

/**
 * Gets a summary of collected questionnaire data for display
 * @param questionnaireData - Data collected from the questionnaire
 * @returns Object with summary information
 */
export const getQuestionnaireSummary = (
  questionnaireData: Partial<QuestionnaireData>
) => {
  const summary = {
    name: questionnaireData.basics?.name || 'Unknown',
    relationship: questionnaireData.basics?.relationship || 'Unknown',
    hasBasicInfo: !!(questionnaireData.basics?.name && questionnaireData.basics?.relationship),
    hasDates: !!(questionnaireData.dates?.birthday || questionnaireData.dates?.anniversary),
    hasStyle: !!(questionnaireData.style?.favoriteColor || questionnaireData.style?.preferredStyle || questionnaireData.style?.brands?.length),
    hasInterests: !!(questionnaireData.interests?.interests?.length || questionnaireData.interests?.dislikes?.length),
    hasPractical: !!(questionnaireData.practical?.sizes || questionnaireData.practical?.budget),
    completionPercentage: 0,
  };

  // Calculate completion percentage
  const sections = [
    summary.hasBasicInfo,
    summary.hasDates,
    summary.hasStyle,
    summary.hasInterests,
    summary.hasPractical,
  ];
  
  summary.completionPercentage = Math.round(
    (sections.filter(Boolean).length / sections.length) * 100
  );

  return summary;
};
