import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Platform } from 'react-native';
import { useRouter } from 'expo-router';
import { Feather } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useQuestionnaire } from '../../../contexts/QuestionnaireContext';
import StepLayout from '../../../components/questionnaire/StepLayout';
import * as Haptics from 'expo-haptics';

const TOTAL_STEPS = 5;

export default function Step2DatesScreen() {
  const router = useRouter();
  const { data, updateData, nextStep } = useQuestionnaire();

  const [birthday, setBirthday] = useState<Date | undefined>(data.dates?.birthday);
  const [anniversary, setAnniversary] = useState<Date | undefined>(data.dates?.anniversary);
  const [showBirthdayPicker, setShowBirthdayPicker] = useState(false);
  const [showAnniversaryPicker, setShowAnniversaryPicker] = useState(false);

  // Update context when local state changes
  useEffect(() => {
    updateData({
      dates: {
        birthday,
        anniversary,
      },
    });
  }, [birthday, anniversary, updateData]);

  const handleNext = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    nextStep();
    router.push('/(onboarding)/questionnaire/step-3-style' as any);
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const handleBirthdayChange = (event: any, selectedDate?: Date) => {
    setShowBirthdayPicker(Platform.OS === 'ios');
    if (selectedDate) {
      setBirthday(selectedDate);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };

  const handleAnniversaryChange = (event: any, selectedDate?: Date) => {
    setShowAnniversaryPicker(Platform.OS === 'ios');
    if (selectedDate) {
      setAnniversary(selectedDate);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };

  const clearBirthday = () => {
    setBirthday(undefined);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const clearAnniversary = () => {
    setAnniversary(undefined);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  const getPersonName = () => {
    return data.basics?.name || 'they';
  };

  return (
    <StepLayout totalSteps={TOTAL_STEPS} onNext={handleNext}>
      <View style={styles.container}>
        <Text style={styles.title}>Never miss their special days</Text>
        <Text style={styles.subtitle}>
          Add important dates so you'll always be prepared with the perfect gift
        </Text>

        {/* Birthday Section */}
        <View style={styles.dateSection}>
          <View style={styles.dateHeader}>
            <Text style={styles.dateLabel}>🎂 Birthday</Text>
            <Text style={styles.dateOptional}>Optional</Text>
          </View>

          <TouchableOpacity
            style={[styles.dateButton, birthday && styles.dateButtonSelected]}
            onPress={() => setShowBirthdayPicker(true)}
            activeOpacity={0.7}
          >
            <View style={styles.dateButtonContent}>
              <Feather
                name="calendar"
                size={20}
                color={birthday ? '#A3002B' : '#666'}
              />
              <Text style={[
                styles.dateButtonText,
                birthday && styles.dateButtonTextSelected
              ]}>
                {birthday ? formatDate(birthday) : `When is ${getPersonName()}'s birthday?`}
              </Text>
            </View>
            {birthday && (
              <TouchableOpacity
                style={styles.clearButton}
                onPress={clearBirthday}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <Feather name="x" size={16} color="#666" />
              </TouchableOpacity>
            )}
          </TouchableOpacity>

          {showBirthdayPicker && (
            <DateTimePicker
              value={birthday || new Date()}
              mode="date"
              display={Platform.OS === 'ios' ? 'spinner' : 'default'}
              onChange={handleBirthdayChange}
              maximumDate={new Date()}
            />
          )}
        </View>

        {/* Anniversary Section */}
        <View style={styles.dateSection}>
          <View style={styles.dateHeader}>
            <Text style={styles.dateLabel}>💕 Anniversary</Text>
            <Text style={styles.dateOptional}>Optional</Text>
          </View>

          <TouchableOpacity
            style={[styles.dateButton, anniversary && styles.dateButtonSelected]}
            onPress={() => setShowAnniversaryPicker(true)}
            activeOpacity={0.7}
          >
            <View style={styles.dateButtonContent}>
              <Feather
                name="heart"
                size={20}
                color={anniversary ? '#A3002B' : '#666'}
              />
              <Text style={[
                styles.dateButtonText,
                anniversary && styles.dateButtonTextSelected
              ]}>
                {anniversary ? formatDate(anniversary) : 'Add an anniversary date'}
              </Text>
            </View>
            {anniversary && (
              <TouchableOpacity
                style={styles.clearButton}
                onPress={clearAnniversary}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <Feather name="x" size={16} color="#666" />
              </TouchableOpacity>
            )}
          </TouchableOpacity>

          {showAnniversaryPicker && (
            <DateTimePicker
              value={anniversary || new Date()}
              mode="date"
              display={Platform.OS === 'ios' ? 'spinner' : 'default'}
              onChange={handleAnniversaryChange}
              maximumDate={new Date()}
            />
          )}
        </View>

        {/* Helpful tip */}
        <View style={styles.tipContainer}>
          <Feather name="info" size={16} color="#A3002B" />
          <Text style={styles.tipText}>
            Don't worry if you don't know exact dates - you can always add them later!
          </Text>
        </View>
      </View>
    </StepLayout>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 8,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
    color: '#1a1a1a',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    color: '#666',
    marginBottom: 32,
    lineHeight: 22,
  },
  dateSection: {
    marginBottom: 24,
  },
  dateHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  dateLabel: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1a1a1a',
  },
  dateOptional: {
    fontSize: 14,
    color: '#A3002B',
    fontWeight: '500',
  },
  dateButton: {
    borderWidth: 2,
    borderColor: '#e1e5e9',
    borderRadius: 16,
    padding: 16,
    backgroundColor: '#fff',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  dateButtonSelected: {
    borderColor: '#A3002B',
    backgroundColor: '#fef7f7',
  },
  dateButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  dateButtonText: {
    fontSize: 16,
    marginLeft: 12,
    color: '#666',
    flex: 1,
  },
  dateButtonTextSelected: {
    color: '#A3002B',
    fontWeight: '500',
  },
  clearButton: {
    padding: 4,
    marginLeft: 8,
  },
  tipContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: '#fef7f7',
    padding: 16,
    borderRadius: 12,
    marginTop: 16,
  },
  tipText: {
    fontSize: 14,
    color: '#A3002B',
    marginLeft: 8,
    flex: 1,
    lineHeight: 20,
  },
});